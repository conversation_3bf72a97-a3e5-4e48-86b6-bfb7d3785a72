'use client';

import { useState, useEffect } from 'react';
import { petStorage, useStorageListener } from '@/lib/storage';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Heart, 
  Calendar, 
  Clock, 
  Utensils, 
  Trash2, 
  Syringe, 
  StickyNote,
  Plus,
  PawPrint
} from 'lucide-react';
import { FeedingTracker } from '@/components/feeding-tracker';
import { LitterTracker } from '@/components/litter-tracker';
import { VaccinationTracker } from '@/components/vaccination-tracker';
import { NotesTracker } from '@/components/notes-tracker';
import { PetProfile } from '@/components/pet-profile';

interface Activity {
  id: string;
  type: 'feeding' | 'litter' | 'vaccination' | 'note';
  title: string;
  timestamp: string;
  details?: string;
}

export default function Home() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [petName, setPetName] = useState('Your Pet');
  const [petImage, setPetImage] = useState<string>('');
  const [todayMealsCount, setTodayMealsCount] = useState(0);
  const [isClient, setIsClient] = useState(false);

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return; // Only run on client side

    // Load recent activities from localStorage
    const loadRecentActivities = () => {
      const feeding = petStorage.getFeedingLogs();
      const litter = petStorage.getLitterLogs();
      const vaccinations = petStorage.getVaccinations();
      const notes = petStorage.getNotes();
      const profile = petStorage.getProfile() as any;

      if (profile.name) {
        setPetName(profile.name);
      }

      if (profile.image) {
        setPetImage(profile.image);
      } else {
        setPetImage('');
      }

      // Calculate today's meals count
      const todayMeals = feeding.filter((f: any) =>
        new Date(f.timestamp).toDateString() === new Date().toDateString()
      ).length;
      setTodayMealsCount(todayMeals);

      const allActivities: Activity[] = [
        ...feeding.map((f: any) => ({
          id: f.id,
          type: 'feeding' as const,
          title: `Fed ${f.foodType}`,
          timestamp: f.timestamp,
          details: f.amount
        })),
        ...litter.map((l: any) => ({
          id: l.id,
          type: 'litter' as const,
          title: `Litter ${l.type}`,
          timestamp: l.timestamp,
          details: l.notes
        })),
        ...vaccinations.map((v: any) => ({
          id: v.id,
          type: 'vaccination' as const,
          title: `${v.vaccine} vaccine`,
          timestamp: v.date,
          details: v.veterinarian
        })),
        ...notes.map((n: any) => ({
          id: n.id,
          type: 'note' as const,
          title: n.topic,
          timestamp: n.timestamp,
          details: n.description.substring(0, 100) + '...'
        }))
      ];

      // Sort by timestamp and take the 5 most recent
      const sortedActivities = allActivities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 5);

      setActivities(sortedActivities);
    };

    loadRecentActivities();

    // Listen for storage changes
    const cleanup = useStorageListener(() => {
      loadRecentActivities();
    });

    return cleanup;
  }, [isClient]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'feeding': return <Utensils className="h-4 w-4" />;
      case 'litter': return <Trash2 className="h-4 w-4" />;
      case 'vaccination': return <Syringe className="h-4 w-4" />;
      case 'note': return <StickyNote className="h-4 w-4" />;
      default: return <Heart className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'feeding': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'litter': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'vaccination': return 'bg-green-100 text-green-800 border-green-200';
      case 'note': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
              {petImage ? (
                <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">

                  <img
                    src={petImage}
                    alt={`Photo of ${petName}`}
                    className="h-16 w-16 object-cover rounded-md"
                  />
                </div>
              ) : (
                            <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-4 rounded-xl shadow-lg">
                              <PawPrint className="h-8 w-8 text-white" />
                            </div>
              )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Pet Tracker</h1>
              <p className="text-gray-600">Caring for {petName}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Today</p>
            <p className="text-lg font-semibold text-gray-900">
              {new Date().toLocaleDateString([], { 
                weekday: 'long', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>

        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8">
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              <Heart className="h-4 w-4" />
              <span className="hidden sm:inline">Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="feeding" className="flex items-center space-x-2">
              <Utensils className="h-4 w-4" />
              <span className="hidden sm:inline">Feeding</span>
            </TabsTrigger>
            <TabsTrigger value="litter" className="flex items-center space-x-2">
              <Trash2 className="h-4 w-4" />
              <span className="hidden sm:inline">Litter</span>
            </TabsTrigger>
            <TabsTrigger value="vaccination" className="flex items-center space-x-2">
              <Syringe className="h-4 w-4" />
              <span className="hidden sm:inline">Health</span>
            </TabsTrigger>
            <TabsTrigger value="notes" className="flex items-center space-x-2">
              <StickyNote className="h-4 w-4" />
              <span className="hidden sm:inline">Notes</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <Utensils className="h-6 w-6 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Today's Meals</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {petStorage.getFeedingLogs()
                          .filter((f: any) => new Date(f.timestamp).toDateString() === new Date().toDateString())
                          .length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <Trash2 className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Litter Cleaned</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {petStorage.getLitterLogs()
                          .filter((l: any) => new Date(l.timestamp).toDateString() === new Date().toDateString())
                          .length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="bg-green-100 p-3 rounded-lg">
                      <Syringe className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Vaccinations</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {petStorage.getVaccinations().length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="bg-purple-100 p-3 rounded-lg">
                      <StickyNote className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Notes</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {petStorage.getNotes().length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="h-5 w-5" />
                    <span>Recent Activity</span>
                  </CardTitle>
                  <CardDescription>
                    Latest updates for {petName}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activities.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No recent activity</p>
                    ) : (
                      activities.map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                          <Badge variant="outline" className={getActivityColor(activity.type)}>
                            {getActivityIcon(activity.type)}
                          </Badge>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                            {activity.details && (
                              <p className="text-sm text-gray-600 truncate">{activity.details}</p>
                            )}
                            <p className="text-xs text-gray-500 mt-1">{formatTimestamp(activity.timestamp)}</p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Pet Profile */}
              <PetProfile onUpdate={() => {
                const profile = petStorage.getProfile() as any;
                if (profile.name) setPetName(profile.name);
                if (profile.image) {
                  setPetImage(profile.image);
                } else {
                  setPetImage('');
                }
              }} />
            </div>
          </TabsContent>

          <TabsContent value="feeding">
            <FeedingTracker />
          </TabsContent>

          <TabsContent value="litter">
            <LitterTracker />
          </TabsContent>

          <TabsContent value="vaccination">
            <VaccinationTracker />
          </TabsContent>

          <TabsContent value="notes">
            <NotesTracker />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
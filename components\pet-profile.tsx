'use client';

import { useState, useEffect } from 'react';
import { petStorage } from '@/lib/storage';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { PawPrint, Edit, Save, X } from 'lucide-react';

interface PetProfileData {
  name: string;
  species: string;
  breed: string;
  age: string;
  weight: string;
  color: string;
  notes: string;
}

interface PetProfileProps {
  onUpdate: () => void;
}

export function PetProfile({ onUpdate }: PetProfileProps) {
  const [profile, setProfile] = useState<PetProfileData>({
    name: '',
    species: '',
    breed: '',
    age: '',
    weight: '',
    color: '',
    notes: ''
  });
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const savedProfile = petStorage.getProfile() as Partial<PetProfileData>;
    // Ensure all fields are strings to prevent controlled/uncontrolled input issues
    setProfile({
      name: savedProfile.name || '',
      species: savedProfile.species || '',
      breed: savedProfile.breed || '',
      age: savedProfile.age || '',
      weight: savedProfile.weight || '',
      color: savedProfile.color || '',
      notes: savedProfile.notes || ''
    });
  }, []);

  const handleSave = () => {
    petStorage.setProfile(profile);
    setIsEditing(false);
    onUpdate();
  };

  const handleCancel = () => {
    const savedProfile = petStorage.getProfile() as Partial<PetProfileData>;
    // Ensure all fields are strings to prevent controlled/uncontrolled input issues
    setProfile({
      name: savedProfile.name || '',
      species: savedProfile.species || '',
      breed: savedProfile.breed || '',
      age: savedProfile.age || '',
      weight: savedProfile.weight || '',
      color: savedProfile.color || '',
      notes: savedProfile.notes || ''
    });
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <PawPrint className="h-5 w-5" />
            <CardTitle>Pet Profile</CardTitle>
          </div>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave}>
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
            </div>
          )}
        </div>
        <CardDescription>
          {profile.name ? `Information about ${profile.name}` : 'Add your pet\'s information'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isEditing ? (
          <div className="space-y-3">
            {profile.name ? (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Name</p>
                    <p className="text-lg font-semibold">{profile.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Species</p>
                    <p className="text-lg font-semibold">{profile.species || 'Not specified'}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Breed</p>
                    <p>{profile.breed || 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Age</p>
                    <p>{profile.age || 'Not specified'}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Weight</p>
                    <p>{profile.weight || 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Color</p>
                    <p>{profile.color || 'Not specified'}</p>
                  </div>
                </div>
                {profile.notes && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Notes</p>
                    <p className="text-sm text-gray-700">{profile.notes}</p>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <PawPrint className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No pet profile set up yet</p>
                <p className="text-sm">Click Edit to add your pet's information</p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Pet Name *</Label>
                <Input
                  id="name"
                  value={profile.name}
                  onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  placeholder="Enter pet's name"
                />
              </div>
              <div>
                <Label htmlFor="species">Species</Label>
                <Select value={profile.species} onValueChange={(value) => setProfile({ ...profile, species: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select species" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cat">Cat</SelectItem>
                    <SelectItem value="dog">Dog</SelectItem>
                    <SelectItem value="rabbit">Rabbit</SelectItem>
                    <SelectItem value="bird">Bird</SelectItem>
                    <SelectItem value="fish">Fish</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="breed">Breed</Label>
                <Input
                  id="breed"
                  value={profile.breed}
                  onChange={(e) => setProfile({ ...profile, breed: e.target.value })}
                  placeholder="Enter breed"
                />
              </div>
              <div>
                <Label htmlFor="age">Age</Label>
                <Input
                  id="age"
                  value={profile.age}
                  onChange={(e) => setProfile({ ...profile, age: e.target.value })}
                  placeholder="e.g., 2 years, 6 months"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weight">Weight</Label>
                <Input
                  id="weight"
                  value={profile.weight}
                  onChange={(e) => setProfile({ ...profile, weight: e.target.value })}
                  placeholder="e.g., 12 lbs, 5.5 kg"
                />
              </div>
              <div>
                <Label htmlFor="color">Color/Markings</Label>
                <Input
                  id="color"
                  value={profile.color}
                  onChange={(e) => setProfile({ ...profile, color: e.target.value })}
                  placeholder="e.g., Orange tabby"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={profile.notes}
                onChange={(e) => setProfile({ ...profile, notes: e.target.value })}
                placeholder="Any special notes about your pet..."
                className="min-h-[80px]"
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
interface StorageItem<T> {
  data: T;
  timestamp: number;
  version: string;
}

class LocalStorageManager {
  private readonly version = '1.0.0';

  private serialize<T>(data: T): string {
    const item: StorageItem<T> = {
      data,
      timestamp: Date.now(),
      version: this.version
    };
    return JSON.stringify(item);
  }

  private deserialize<T>(serialized: string): T | null {
    try {
      const item: StorageItem<T> = JSON.parse(serialized);
      // Could add version migration logic here if needed
      return item.data;
    } catch (error) {
      console.warn('Failed to deserialize storage item:', error);
      return null;
    }
  }

  set<T>(key: string, data: T): boolean {
    try {
      const serialized = this.serialize(data);
      localStorage.setItem(key, serialized);
      this.notifyChange(key);
      return true;
    } catch (error) {
      console.error('Failed to save to storage:', error);
      return false;
    }
  }

  get<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      if (!item) return defaultValue;
      
      const data = this.deserialize<T>(item);
      return data !== null ? data : defaultValue;
    } catch (error) {
      console.error('Failed to read from storage:', error);
      return defaultValue;
    }
  }

  update<T>(key: string, updater: (current: T) => T, defaultValue: T): T {
    const current = this.get(key, defaultValue);
    const updated = updater(current);
    this.set(key, updated);
    return updated;
  }

  remove(key: string): boolean {
    try {
      localStorage.removeItem(key);
      this.notifyChange(key);
      return true;
    } catch (error) {
      console.error('Failed to remove from storage:', error);
      return false;
    }
  }

  clear(): boolean {
    try {
      localStorage.clear();
      this.notifyChange('*');
      return true;
    } catch (error) {
      console.error('Failed to clear storage:', error);
      return false;
    }
  }

  private notifyChange(key: string): void {
    window.dispatchEvent(new CustomEvent('pet-data-updated', { 
      detail: { key, timestamp: Date.now() } 
    }));
  }

  // Get all keys with a specific prefix
  getKeys(prefix?: string): string[] {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (!prefix || key.startsWith(prefix))) {
        keys.push(key);
      }
    }
    return keys;
  }

  // Get storage usage info
  getStorageInfo(): { used: number; available: number; percentage: number } {
    let used = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        used += key.length + (localStorage.getItem(key)?.length || 0);
      }
    }
    
    // Rough estimate of localStorage limit (usually 5-10MB)
    const estimated = 5 * 1024 * 1024; // 5MB
    return {
      used,
      available: estimated - used,
      percentage: (used / estimated) * 100
    };
  }
}

// Create singleton instance
export const storage = new LocalStorageManager();

// Typed storage hooks for specific data types
export const petStorage = {
  // Pet profile
  getProfile: () => storage.get('pet-profile', {}),
  setProfile: (profile: any) => storage.set('pet-profile', profile),
  updateProfile: (updater: (current: any) => any) => 
    storage.update('pet-profile', updater, {}),

  // Feeding logs
  getFeedingLogs: () => storage.get('feeding-logs', []),
  setFeedingLogs: (logs: any[]) => storage.set('feeding-logs', logs),
  addFeedingLog: (log: any) => 
    storage.update('feeding-logs', (logs) => [log, ...logs], []),
  removeFeedingLog: (id: string) =>
    storage.update('feeding-logs', (logs) => logs.filter((l: any) => l.id !== id), []),

  // Litter logs
  getLitterLogs: () => storage.get('litter-logs', []),
  setLitterLogs: (logs: any[]) => storage.set('litter-logs', logs),
  addLitterLog: (log: any) =>
    storage.update('litter-logs', (logs) => [log, ...logs], []),
  removeLitterLog: (id: string) =>
    storage.update('litter-logs', (logs) => logs.filter((l: any) => l.id !== id), []),

  // Vaccinations
  getVaccinations: () => storage.get('vaccinations', []),
  setVaccinations: (vaccinations: any[]) => storage.set('vaccinations', vaccinations),
  addVaccination: (vaccination: any) =>
    storage.update('vaccinations', (vaccinations) => [vaccination, ...vaccinations], []),
  removeVaccination: (id: string) =>
    storage.update('vaccinations', (vaccinations) => vaccinations.filter((v: any) => v.id !== id), []),

  // Notes
  getNotes: () => storage.get('notes', []),
  setNotes: (notes: any[]) => storage.set('notes', notes),
  addNote: (note: any) =>
    storage.update('notes', (notes) => [note, ...notes], []),
  removeNote: (id: string) =>
    storage.update('notes', (notes) => notes.filter((n: any) => n.id !== id), []),
};

// React hook for listening to storage changes
export function useStorageListener(callback: (key: string) => void) {
  if (typeof window !== 'undefined') {
    const handleStorageChange = (event: CustomEvent) => {
      callback(event.detail.key);
    };

    window.addEventListener('pet-data-updated', handleStorageChange as EventListener);
    
    return () => {
      window.removeEventListener('pet-data-updated', handleStorageChange as EventListener);
    };
  }
  return () => {};
}